<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "Solutions",
});

// 页面加载动画
const isLoaded = ref(false);
const visibleSections = ref({
  title: false,
  overview: false,
  painPoints: false,
  advantages: false,
  values: false,
});

onMounted(() => {
  // 设置页面已加载
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);

  // 逐步显示各个部分
  setTimeout(() => {
    visibleSections.value.title = true;

    setTimeout(() => {
      visibleSections.value.overview = true;

      setTimeout(() => {
        visibleSections.value.painPoints = true;

        setTimeout(() => {
          visibleSections.value.advantages = true;

          setTimeout(() => {
            visibleSections.value.values = true;
          }, 200);
        }, 200);
      }, 200);
    }, 200);
  }, 500);
});
</script>

<template>
  <div class="solutions-page" :class="{ 'is-loaded': isLoaded }">
    <!-- 方案概述 -->
    <section class="solutions-overview">
      <div class="container">
        <h2 class="section-title" :class="{ visible: visibleSections.title }">
          <span class="title-text">智慧水利数字孪生</span>
        </h2>
        <div
          class="overview-content"
          :class="{ visible: visibleSections.overview }"
        >
          <div class="overview-description">
            智慧水利数字孪生解决方案是通过构建虚实映射的数字流域，实现水利管理全要素数字化、全流程智能化的新一代技术体系。该方案整合物联网监测、遥感测绘、气象水文等多源数据，依托三维GIS、大数据分析和AI算法，建立高精度水利孪生模型，实现对物理流域的实时监测、动态仿真和智能决策。通过融合洪水演进模拟、水资源优化调度、工程安全预警等核心功能，可显著提升防汛响应速度和水资源利用效率，为各级区域及流域的水旱灾害防御、水资源优化配置、水环境治理、水生态保护和水文化服务等业务开展提供管理手段和决策依据，助力建设人与自然和谐共生的现代化生态。
          </div>
        </div>

        <div class="solution-list">
          <div
            class="solution-item pain-points"
            :class="{ visible: visibleSections.painPoints }"
          >
            <div class="solution-item-title">
              <div class="title-icon">
                <Icon icon="mdi:alert-circle" />
              </div>
              <div class="title-text">行业痛点</div>
            </div>
            <div class="solution-item-content">
              <div class="solution-item-column">
                <div class="solution-item-column-item">
                  <div class="item-number">01</div>
                  <div class="item-text">
                    传统水利管理依赖人工经验和静态数据，难以应对极端天气和突发水情
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-number">02</div>
                  <div class="item-text">
                    各部门数据孤岛严重，决策缺乏实时动态数据支撑
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-number">03</div>
                  <div class="item-text">
                    防汛应急响应效率低，灾情评估和调度方案制定滞后
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-number">04</div>
                  <div class="item-text">
                    水资源管理粗放，难以实现精细化调度
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="solution-item advantages"
            :class="{ visible: visibleSections.advantages }"
          >
            <div class="solution-item-title">
              <div class="title-icon">
                <Icon icon="mdi:star" />
              </div>
              <div class="title-text">方案优势</div>
            </div>
            <div class="solution-item-content">
              <div class="solution-item-column">
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:database" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">全要素数字化</div>
                    <div class="item-desc">
                      建立高精度三维流域模型，集成水利专业算法
                    </div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:chart-timeline-variant" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">实时动态仿真</div>
                    <div class="item-desc">
                      分钟级数据更新，支持72小时洪水演进预测
                    </div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:brain" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">智能决策支持</div>
                    <div class="item-desc">
                      内置专家知识库，自动生成多套调度方案比选
                    </div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:lan-connect" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">多业务协同</div>
                    <div class="item-desc">
                      打破数据壁垒，实现跨部门、跨层级业务协同
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="solution-item values"
            :class="{ visible: visibleSections.values }"
          >
            <div class="solution-item-title">
              <div class="title-icon">
                <Icon icon="mdi:chart-bar" />
              </div>
              <div class="title-text">客户价值</div>
            </div>
            <div class="solution-item-content">
              <div class="solution-item-column">
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:shield" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">防汛减灾</div>
                    <div class="item-desc">提升预警提前期，减少灾害损失</div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:water" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">调度优化</div>
                    <div class="item-desc">
                      提高水资源利用效率，年节水效益超额
                    </div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:speedometer" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">管理提效</div>
                    <div class="item-desc">
                      减少人工巡查工作量，降低运维成本
                    </div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:chart-donut" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">科学决策</div>
                    <div class="item-desc">
                      提供数据支撑，助力水利工程规划建设
                    </div>
                  </div>
                </div>
                <div class="solution-item-column-item">
                  <div class="item-icon">
                    <Icon icon="mdi:trending-up" />
                  </div>
                  <div class="item-content">
                    <div class="item-header">长效发展</div>
                    <div class="item-desc">
                      构建智慧水利底座，业务持续创新升级
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 背景装饰元素 -->
      <div class="decoration-elements">
        <div class="deco-circle c1"></div>
        <div class="deco-circle c2"></div>
        <div class="deco-circle c3"></div>
        <div class="deco-line l1"></div>
        <div class="deco-line l2"></div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.solutions-page {
  height: auto;
  overflow-y: auto;
  position: relative;
  color: #fff;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url("@/assets/images/banner3.png");
    background-size: cover;
    background-position: center;
    z-index: -1;
    filter: brightness(0.85) contrast(1.1) saturate(1.1);
    transition: all 1.2s ease;
    transform: scale(1.05);
  }

  &.is-loaded::before {
    transform: scale(1);
  }

  &::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.2)
    );
    z-index: -1;
  }

  .solutions-overview {
    padding: 150px 0 100px;
    position: relative;
    overflow: hidden;

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 40px;
      position: relative;
      z-index: 2;

      .section-title {
        font-size: 46px;
        text-align: center;
        margin: 0 0 60px;
        position: relative;
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);

        &.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .title-text {
          position: relative;
          display: inline-block;
          padding: 0 15px;

          &::before,
          &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 60px;
            height: 2px;
            background: linear-gradient(to right, transparent, #1e88e5);
          }

          &::before {
            right: 100%;
            background: linear-gradient(to right, #1e88e5, transparent);
          }

          &::after {
            left: 100%;
          }
        }
      }

      .overview-content {
        margin-bottom: 80px;
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;

        &.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .overview-description {
          font-size: 18px;
          line-height: 1.8;
          margin: 0 auto;
          text-align: justify;
          max-width: 90%;
          background-color: rgba(0, 0, 0, 0.2);
          padding: 35px;
          border-radius: 16px;
          border-left: 4px solid #1e88e5;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
      }

      .solution-list {
        .solution-item {
          opacity: 0;
          transform: translateY(40px);
          transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);

          &.visible {
            opacity: 1;
            transform: translateY(0);
          }

          &.pain-points {
            transition-delay: 0.2s;
          }

          &.advantages {
            transition-delay: 0.4s;
          }

          &.values {
            transition-delay: 0.6s;
          }

          &-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            position: relative;

            .title-icon {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              background: linear-gradient(135deg, #1e88e5, #1565c0);
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 20px;
              box-shadow: 0 8px 20px rgba(21, 101, 192, 0.3);

              svg {
                font-size: 30px;
                color: white;
              }
            }

            .title-text {
              font-size: 36px;
              font-weight: bold;
              background: linear-gradient(to right, #fff, #90caf9);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              position: relative;

              &::after {
                content: "";
                position: absolute;
                bottom: -8px;
                left: 0;
                width: 100%;
                height: 2px;
                background: linear-gradient(to right, #1e88e5, transparent);
              }
            }
          }

          &-content {
            margin-bottom: 80px;

            .solution-item-column {
              display: grid;
              gap: 30px;

              &-item {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 16px;
                padding: 25px 30px;
                transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(5px);
                display: flex;
                align-items: center;

                &:hover {
                  background: rgba(255, 255, 255, 0.1);
                  transform: translateY(-5px);
                  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                  border-color: rgba(30, 136, 229, 0.3);
                }

                .item-number {
                  font-size: 40px;
                  font-weight: bold;
                  color: #1e88e5;
                  margin-right: 25px;
                  line-height: 1;
                  text-shadow: 0 0 15px rgba(30, 136, 229, 0.4);
                  min-width: 70px;
                }

                .item-text {
                  font-size: 18px;
                  line-height: 1.6;
                }

                .item-icon {
                  width: 50px;
                  height: 50px;
                  border-radius: 12px;
                  background: linear-gradient(
                    135deg,
                    rgba(30, 136, 229, 0.2),
                    rgba(21, 101, 192, 0.2)
                  );
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 20px;
                  transition: all 0.4s ease;

                  svg {
                    font-size: 28px;
                    color: #64b5f6;
                    transition: all 0.4s ease;
                  }
                }

                &:hover .item-icon {
                  background: linear-gradient(
                    135deg,
                    rgba(30, 136, 229, 0.3),
                    rgba(21, 101, 192, 0.3)
                  );
                  transform: rotate(10deg);

                  svg {
                    color: #90caf9;
                    transform: scale(1.1);
                  }
                }

                .item-content {
                  flex: 1;

                  .item-header {
                    font-size: 20px;
                    font-weight: bold;
                    color: #90caf9;
                    margin-bottom: 8px;
                  }

                  .item-desc {
                    font-size: 16px;
                    color: rgba(255, 255, 255, 0.8);
                  }
                }
              }
            }
          }

          // 行业痛点样式
          &.pain-points {
            .solution-item-column {
              grid-template-columns: repeat(2, 1fr);
            }
          }

          // 方案优势样式
          &.advantages {
            .solution-item-column {
              grid-template-columns: repeat(2, 1fr);
            }
          }

          // 客户价值样式
          &.values {
            .solution-item-column {
              grid-template-columns: repeat(3, 1fr);
            }
          }
        }
      }
    }
  }

  // 装饰元素
  .decoration-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;

    .deco-circle {
      position: absolute;
      border-radius: 50%;
      border: 1px solid rgba(30, 136, 229, 0.1);

      &.c1 {
        width: 300px;
        height: 300px;
        top: 10%;
        left: -150px;
      }

      &.c2 {
        width: 500px;
        height: 500px;
        bottom: 10%;
        right: -250px;
        border-width: 2px;
        border-color: rgba(30, 136, 229, 0.05);
      }

      &.c3 {
        width: 200px;
        height: 200px;
        top: 40%;
        right: 10%;
      }
    }

    .deco-line {
      position: absolute;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(30, 136, 229, 0.1),
        transparent
      );
      height: 1px;

      &.l1 {
        width: 100%;
        top: 30%;
        left: 0;
        transform: rotate(-5deg);
      }

      &.l2 {
        width: 80%;
        bottom: 20%;
        right: 0;
        transform: rotate(3deg);
      }
    }
  }
}

// 媒体查询
@media (max-width: 1200px) {
  .solutions-page {
    .solutions-overview {
      .container {
        .solution-list {
          .solution-item {
            &.advantages .solution-item-column,
            &.pain-points .solution-item-column {
              grid-template-columns: 1fr;
            }

            &.values .solution-item-column {
              grid-template-columns: repeat(2, 1fr);
            }

            &-title {
              margin-bottom: 30px;

              .title-text {
                font-size: 32px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .solutions-page {
    .solutions-overview {
      padding: 120px 0 80px;

      .container {
        padding: 0 20px;

        .section-title {
          font-size: 36px;
          margin-bottom: 40px;
        }

        .overview-content {
          margin-bottom: 60px;

          .overview-description {
            font-size: 16px;
            padding: 25px;
            max-width: 100%;
          }
        }

        .solution-list {
          .solution-item {
            &.values .solution-item-column {
              grid-template-columns: 1fr;
            }

            &-title {
              .title-icon {
                width: 50px;
                height: 50px;

                svg {
                  font-size: 24px;
                }
              }

              .title-text {
                font-size: 28px;
              }
            }

            &-content {
              margin-bottom: 60px;

              .solution-item-column-item {
                padding: 20px;

                .item-number {
                  font-size: 32px;
                  margin-right: 15px;
                  min-width: 50px;
                }

                .item-text {
                  font-size: 16px;
                }

                .item-content {
                  .item-header {
                    font-size: 18px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .decoration-elements {
      display: none;
    }
  }
}
</style>
