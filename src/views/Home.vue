<script setup>
import { useRouter } from "vue-router";
import { Icon } from "@iconify/vue";
import { onMounted, ref } from "vue";

defineOptions({
  name: "Home",
});

const router = useRouter();
const isLoaded = ref(false);

onMounted(() => {
  // 添加页面加载动画效果
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);
});

const handleProducts = () => {
  router.push("/products");
};
</script>

<template>
  <div class="home" :class="{ 'is-loaded': isLoaded }">
    <!-- Banner部分 -->
    <section class="banner">
      <div class="banner-content">
        <div class="animated-element title-container">
          <h1 class="main-title">
            <img
              src="@/assets/images/home-title-left.png"
              alt="智慧水生态"
              class="title-img title-left"
            />
            <img
              src="@/assets/images/home-title-right.png"
              alt="数智新未来"
              class="title-img title-right"
            />
          </h1>
        </div>
        <p class="sub-title animated-element">
          智慧水利全场景数字化解决方案服务商
        </p>
        <div class="banner-btn animated-element">
          <a
            href="javascript:void(0)"
            class="explore-btn"
            @click="handleProducts"
          >
            <span class="btn-text">探索怀川科技</span>
            <span class="btn-icon">
              <Icon icon="mdi:arrow-right" class="arrow-icon" />
            </span>
          </a>
        </div>
      </div>
      <div class="floating-particles">
        <div class="particle p1"></div>
        <div class="particle p2"></div>
        <div class="particle p3"></div>
        <div class="particle p4"></div>
        <div class="particle p5"></div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.home {
  height: auto;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  color: #fff;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url("@/assets/images/banner1.png");
    background-size: cover;
    background-position: center;
    z-index: -1;
    filter: brightness(0.85) contrast(1.1) saturate(1.1);
    transition: all 1.5s ease-out;
    transform: scale(1.05);
  }

  &.is-loaded::before {
    transform: scale(1);
  }

  &::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.2)
    );
    z-index: -1;
  }
}

.banner {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 0;
  padding: 0;

  .banner-content {
    position: relative;
    z-index: 10;
    max-width: 800px;
    padding: 0 20px;
  }

  .main-title {
    margin-bottom: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25px;

    .title-img {
      height: 90px;
      object-fit: contain;
      filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
      transition: all 0.5s ease;

      &:hover {
        transform: scale(1.05);
        filter: drop-shadow(0 0 20px rgba(30, 136, 229, 0.6));
      }
    }
  }

  .sub-title {
    font-size: 26px;
    color: #fff;
    margin-bottom: 50px;
    font-weight: 300;
    letter-spacing: 1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  }

  .explore-btn {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #1e88e5, #1565c0);
    color: #fff;
    padding: 15px 35px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.3s;
    box-shadow: 0 5px 15px rgba(21, 101, 192, 0.4);
    position: relative;
    overflow: hidden;
    z-index: 1;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: all 0.6s;
      z-index: -1;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(21, 101, 192, 0.6);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    .btn-text {
      position: relative;
      z-index: 2;
    }

    .btn-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      position: relative;
      z-index: 2;
    }

    .arrow-icon {
      font-size: 22px;
      transition: transform 0.3s;
    }

    &:hover .arrow-icon {
      transform: translateX(5px);
    }
  }
}

// 浮动粒子效果
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 1;

  .particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    pointer-events: none;

    &.p1 {
      width: 80px;
      height: 80px;
      left: 10%;
      top: 20%;
      animation: float 15s infinite ease-in-out;
    }

    &.p2 {
      width: 60px;
      height: 60px;
      right: 15%;
      top: 30%;
      animation: float 18s infinite ease-in-out reverse;
    }

    &.p3 {
      width: 120px;
      height: 120px;
      left: 20%;
      bottom: 15%;
      animation: float 20s infinite ease-in-out 2s;
    }

    &.p4 {
      width: 50px;
      height: 50px;
      right: 25%;
      bottom: 25%;
      animation: float 12s infinite ease-in-out 1s;
    }

    &.p5 {
      width: 40px;
      height: 40px;
      left: 40%;
      top: 15%;
      animation: float 16s infinite ease-in-out 3s;
    }
  }
}

// 页面加载动画
.animated-element {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  .is-loaded & {
    opacity: 1;
    transform: translateY(0);

    &:nth-child(1) {
      transition-delay: 0.2s;
    }

    &:nth-child(2) {
      transition-delay: 0.4s;
    }

    &:nth-child(3) {
      transition-delay: 0.6s;
    }

    &:nth-child(4) {
      transition-delay: 0.8s;
    }
  }
}

// 鼠标滚轮动画
@keyframes scroll {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(15px);
  }
}

// 浮动动画
@keyframes float {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-15px, 15px);
  }
  50% {
    transform: translate(15px, 30px);
  }
  75% {
    transform: translate(15px, -15px);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .banner {
    .main-title {
      gap: 15px;

      .title-img {
        height: 70px;
      }
    }

    .sub-title {
      font-size: 20px;
      margin-bottom: 40px;
    }

    .explore-btn {
      padding: 12px 30px;
      font-size: 16px;
    }
  }

  .floating-particles {
    .particle {
      opacity: 0.5;
    }
  }
}

@media (max-width: 480px) {
  .banner {
    .main-title {
      gap: 10px;

      .title-img {
        height: 50px;
      }
    }

    .sub-title {
      font-size: 18px;
      margin-bottom: 30px;
    }
  }
}
</style>
