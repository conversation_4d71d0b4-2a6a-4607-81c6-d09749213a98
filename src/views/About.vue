<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted, onUnmounted } from "vue";
import { MountainRiverAnimations, presetAnimations } from "@/utils/animations.js";

defineOptions({
  name: "About",
});

// 页面加载动画
const isLoaded = ref(false);
const visibleSections = ref({
  header: false,
  intro: false,
  certificates: false,
  vision: false,
  contact: false,
});

let animations = null;

onMounted(() => {
  // 初始化动画实例
  animations = new MountainRiverAnimations();
  
  // 页面加载效果
  setTimeout(() => {
    isLoaded.value = true;
    
    // 启动英雄区域动画
    presetAnimations.heroSection('.page-header');
    
    // 粒子背景效果
    animations.particleBackground('.page-header', {
      particleCount: 30,
      colors: ['rgba(45, 90, 39, 0.3)', 'rgba(74, 124, 89, 0.3)', 'rgba(107, 144, 128, 0.3)', 'rgba(164, 195, 210, 0.3)']
    });
    
  }, 300);

  // 设置滚动触发动画
  const scrollTriggers = [
    {
      element: '.company-intro',
      onEnter: () => {
        visibleSections.value.intro = true;
        animations.mountainLayersEntry('.intro-content', { stagger: 0.3 });
      }
    },
    {
      element: '.certificates',
      onEnter: () => {
        visibleSections.value.certificates = true;
        animations.cardFlipEntry('.cert-item');
      }
    },
    {
      element: '.vision-mission',
      onEnter: () => {
        visibleSections.value.vision = true;
        animations.mountainLayersEntry('.vision, .mission', { stagger: 0.2 });
      }
    },
    {
      element: '.contact-us',
      onEnter: () => {
        visibleSections.value.contact = true;
        animations.cardFlipEntry('.contact-item');
      }
    }
  ];

  // 创建滚动时间线
  animations.createScrollTimeline(scrollTriggers);

  // 磁性按钮效果
  animations.magneticButton('.cert-item, .contact-item');

  // 视差滚动
  animations.parallaxScroll('.page-header::before');

  // 浮动图标动画
  animations.floatingElements('.floating-icon');

  // 标题文字逐字显现
  setTimeout(() => {
    visibleSections.value.header = true;
    animations.textReveal('.page-header h1');
  }, 800);
});

onUnmounted(() => {
  // 清理动画
  if (animations) {
    animations.cleanup();
  }
});
</script>

<template>
  <div class="about" :class="{ 'is-loaded': isLoaded }">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <h1 :class="{ visible: visibleSections.header }">关于我们</h1>
        <p class="slogan" :class="{ visible: visibleSections.header }">怀数智之力 护青绿山川</p>
        <div class="water-wave"></div>
      </div>
      <!-- 装饰元素 -->
      <div class="decoration-elements">
        <div class="deco-circle c1"></div>
        <div class="deco-circle c2"></div>
        <div class="deco-circle c3"></div>
        <div class="deco-line l1"></div>
        <div class="deco-line l2"></div>
      </div>
    </div>

    <!-- 公司介绍 -->
    <section class="company-intro" :class="{ visible: visibleSections.intro }">
      <div class="container">
        <div class="intro-content">
          <div class="intro-text">
            <h2>怀川科技简介</h2>
            <div class="intro-description">
              <p>
                怀川科技（北京）有限公司，是一家专注于智慧水利行业的现代化科技型企业，主要面向水利行业管理部门用户，提供防洪减灾、水资源管理、水利工程运行管理、河湖管理、水环境保护等业务领域数字化建设相关的规划咨询、产品销售、软件研发和系统集成相关专业服务。
              </p>
              <p>
                公司总部设置在北京，研发中心在江苏，成员主要毕业于河海大学等水利或计算机相关院校和专业，多人具有高级项目管理师资格，有丰富的水利信息化项目管理和实施经验。
              </p>
              <p>
                公司注重锐意革新，致力于利用先进信息技术包括AI大模型、大数据、云计算、卫星遥感、无人机&船等，提升水利行业数字化建设专业服务水平，促进人与自然和谐发展，已于2024年初获得中关村高新技术企业证书，并取得10余项相关软著。
              </p>
            </div>
          </div>
          <div class="intro-image">
            <div class="tech-elements">
              <div class="floating-icon" style="--delay: 0s">
                <Icon icon="mdi:water" />
              </div>
              <div class="floating-icon" style="--delay: 1s">
                <Icon icon="mdi:cloud-outline" />
              </div>
              <div class="floating-icon" style="--delay: 2s">
                <Icon icon="mdi:chip" />
              </div>
              <div class="floating-icon" style="--delay: 3s">
                <Icon icon="mdi:satellite-variant" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 资质荣誉 -->
    <section class="certificates" :class="{ visible: visibleSections.certificates }">
      <div class="container">
        <h2 class="section-title">资质荣誉</h2>
        <div class="cert-grid">
          <div class="cert-item">
            <div class="cert-icon">
              <Icon icon="mdi:certificate" />
            </div>
            <h3>中关村高新技术企业</h3>
            <p>2024年初获得认证</p>
          </div>
          <div class="cert-item">
            <div class="cert-icon">
              <Icon icon="mdi:file-document" />
            </div>
            <h3>软件著作权</h3>
            <p>已取得10余项相关软著</p>
          </div>
          <div class="cert-item">
            <div class="cert-icon">
              <Icon icon="mdi:account-tie" />
            </div>
            <h3>高级项目管理师</h3>
            <p>多人具有专业资格认证</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 愿景使命 -->
    <section class="vision-mission" :class="{ visible: visibleSections.vision }">
      <div class="container">
        <div class="vision-mission-content">
          <div class="vision">
            <h2><Icon icon="mdi:eye" class="section-icon" /> 我们的愿景</h2>
            <p>
              成为智慧水利领域的领军企业，推动水利行业数字化转型，促进人与自然和谐发展。
            </p>
          </div>
          <div class="mission">
            <h2><Icon icon="mdi:flag" class="section-icon" /> 我们的使命</h2>
            <p>
              怀数智之力，护青绿山川，以科技创新驱动水利行业发展，构建水资源智能管理体系。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-us" :class="{ visible: visibleSections.contact }">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-info">
          <div class="contact-item">
            <Icon icon="mdi:map-marker" class="contact-icon" />
            <div>
              <h3>公司总部</h3>
              <p>北京市海淀区中关村科技园</p>
            </div>
          </div>
          <div class="contact-item">
            <Icon icon="mdi:cog" class="contact-icon" />
            <div>
              <h3>研发中心</h3>
              <p>江苏省</p>
            </div>
          </div>
          <div class="contact-item">
            <Icon icon="mdi:email" class="contact-icon" />
            <div>
              <h3>电子邮箱</h3>
              <p><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.about {
  height: auto;
  overflow-y: auto;
  position: relative;
  color: var(--white);
  background: var(--gradient-mountain);

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url("@/assets/images/banner1.png");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    z-index: -2;
    filter: brightness(0.7) contrast(1.2) saturate(1.3);
    transition: all 1.5s cubic-bezier(0.19, 1, 0.22, 1);
    transform: scale(1.05);
  }

  &.is-loaded::before {
    transform: scale(1);
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
  }

  &::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(
      to bottom,
      var(--primary-green-50),
      var(--primary-green-20),
      transparent,
      var(--deep-water-20)
    );
    z-index: -1;
  }
}

.page-header {
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  color: var(--white);
  margin: 0;
  padding: 0;
  overflow: hidden;

  .container {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-5);
    text-align: center;
  }

  h1 {
    font-size: var(--text-5xl);
    font-weight: 700;
    margin-bottom: var(--space-5);
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    background: linear-gradient(135deg, var(--white), var(--lake-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    &::after {
      content: "";
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 4px;
      background: var(--gradient-tech);
      border-radius: var(--radius-full);
      transition: width 1.2s cubic-bezier(0.19, 1, 0.22, 1) 0.5s;
      box-shadow: var(--glow-tech);
    }

    &.visible {
      opacity: 1;
      transform: translateY(0);

      &::after {
        width: 200px;
      }
    }
  }

  .slogan {
    font-size: var(--text-3xl);
    font-weight: 600;
    background: linear-gradient(45deg, var(--white), var(--lake-blue), var(--tech-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-8);
    letter-spacing: 3px;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.3s;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .water-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 120px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z' fill='%23ffffff' fill-opacity='0.15'/%3E%3C/svg%3E")
      repeat-x;
    animation: wave 4s ease-in-out infinite;
    z-index: 5;
  }

  // 装饰元素增强
  .decoration-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;

    .deco-circle {
      position: absolute;
      border-radius: 50%;
      border: 2px solid var(--tech-blue-10);
      background: radial-gradient(circle, var(--tech-blue-10), transparent);
      animation: pulse 4s ease-in-out infinite;

      &.c1 {
        width: 400px;
        height: 400px;
        top: 5%;
        left: -200px;
        animation-delay: 0s;
      }

      &.c2 {
        width: 600px;
        height: 600px;
        bottom: 5%;
        right: -300px;
        border-width: 3px;
        border-color: var(--bamboo-green-20);
        background: radial-gradient(circle, var(--bamboo-green-10), transparent);
        animation-delay: 1s;
      }

      &.c3 {
        width: 250px;
        height: 250px;
        top: 35%;
        right: 8%;
        border-color: var(--river-blue-20);
        background: radial-gradient(circle, var(--river-blue-10), transparent);
        animation-delay: 2s;
      }
    }

    .deco-line {
      position: absolute;
      background: linear-gradient(
        90deg,
        transparent,
        var(--tech-blue-20),
        var(--bamboo-green-20),
        transparent
      );
      height: 2px;
      border-radius: var(--radius-full);
      animation: flow 6s ease-in-out infinite;

      &.l1 {
        width: 100%;
        top: 25%;
        left: 0;
        transform: rotate(-3deg);
        animation-delay: 0s;
      }

      &.l2 {
        width: 80%;
        bottom: 25%;
        right: 0;
        transform: rotate(2deg);
        animation-delay: 2s;
      }
    }
  }
}



.company-intro {
  padding: 120px 0 100px;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .intro-content {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .intro-text {
      flex: 1;
      padding-right: 50px;

      h2 {
        font-size: 32px;
        margin-bottom: 30px;
        color: #fff;
        font-weight: 600;
        position: relative;
        display: inline-block;

        &::after {
          content: "";
          position: absolute;
          bottom: -10px;
          left: 0;
          width: 100%;
          height: 3px;
          background: linear-gradient(
            to right,
            transparent,
            $tech-blue,
            transparent
          );
        }
      }

      .intro-description {
        background-color: rgba(0, 0, 0, 0.2);
        padding: 35px;
        border-radius: 16px;
        border-left: 4px solid $tech-blue;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);

        p {
          font-size: 16px;
          line-height: 1.8;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 20px;
          text-align: justify;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .intro-image {
      flex: 1;
      height: 400px;
      background: linear-gradient(135deg, $accent-green, $water-blue);
      border-radius: 16px;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.1);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.1),
          transparent,
          rgba(255, 255, 255, 0.05)
        );
        pointer-events: none;
      }

      .tech-elements {
        position: relative;
        width: 100%;
        height: 100%;

        .floating-icon {
          position: absolute;
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 24px;
          animation: float 3s ease-in-out infinite;
          animation-delay: var(--delay);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
          }

          &:nth-child(1) {
            top: 20%;
            left: 20%;
          }
          &:nth-child(2) {
            top: 30%;
            right: 20%;
          }
          &:nth-child(3) {
            bottom: 30%;
            left: 30%;
          }
          &:nth-child(4) {
            bottom: 20%;
            right: 30%;
          }
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;

      .intro-text {
        padding-right: 0;
        margin-bottom: 30px;
      }

      .intro-image {
        width: 100%;
      }
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 资质荣誉样式
.certificates {
  padding: 120px 0 100px;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.2s;

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .cert-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    margin-top: 60px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;

    .cert-item {
      background: rgba(255, 255, 255, 0.05);
      padding: 40px 30px;
      border-radius: 20px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
      backdrop-filter: blur(10px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(30, 136, 229, 0.3);
      }

      .cert-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, $tech-blue, $river-blue);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 25px;
        color: #fff;
        font-size: 32px;
        box-shadow: 0 8px 20px rgba(30, 136, 229, 0.3);
        transition: all 0.3s ease;
      }

      &:hover .cert-icon {
        transform: scale(1.1);
        box-shadow: 0 12px 30px rgba(30, 136, 229, 0.4);
      }

      h3 {
        font-size: 20px;
        color: #fff;
        margin-bottom: 15px;
        font-weight: 600;
      }

      p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 15px;
        line-height: 1.5;
      }
    }
  }
}

.vision-mission {
  padding: 120px 0 100px;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.4s;

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(45, 90, 39, 0.8), rgba(74, 124, 89, 0.6));
    backdrop-filter: blur(10px);
  }

  .vision-mission-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 60px;
    text-align: center;
  }

  .vision,
  .mission {
    background: rgba(255, 255, 255, 0.05);
    padding: 50px 40px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);

    &:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
      border-color: rgba(30, 136, 229, 0.3);
    }

    h2 {
      font-size: 28px;
      margin-bottom: 25px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(to right, transparent, $tech-blue, transparent);
      }

      .section-icon {
        margin-right: 15px;
        color: $water-blue;
        font-size: 32px;
        filter: drop-shadow(0 0 10px rgba(164, 195, 210, 0.5));
      }
    }

    p {
      font-size: 18px;
      line-height: 1.8;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.contact-us {
  padding: 120px 0 100px;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.6s;

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .contact-item {
      display: flex;
      align-items: flex-start;
      background: rgba(255, 255, 255, 0.05);
      padding: 40px 30px;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(30, 136, 229, 0.3);
      }

      .contact-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, $tech-blue, $river-blue);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 25px;
        font-size: 28px;
        color: #fff;
        flex-shrink: 0;
        box-shadow: 0 8px 20px rgba(30, 136, 229, 0.3);
        transition: all 0.3s ease;
      }

      &:hover .contact-icon {
        transform: scale(1.1);
        box-shadow: 0 12px 30px rgba(30, 136, 229, 0.4);
      }

      div {
        h3 {
          font-size: 20px;
          color: #fff;
          margin-bottom: 12px;
          font-weight: 600;
        }

        p {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 1.6;
        }
      }

      @media (max-width: 768px) {
        grid-column: 1 / -1;
      }
    }
  }
}

// 通用样式
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 60px;
  color: #fff;
  position: relative;
  font-weight: 600;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

  &:after {
    content: "";
    position: absolute;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, $tech-blue, transparent);
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
  }
}

@keyframes wave {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-50px);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .company-intro .intro-content {
    .intro-text {
      padding-right: 30px;
    }
  }

  .vision-mission .vision-mission-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .page-header {
    h1 {
      font-size: 36px;
    }

    .slogan {
      font-size: 20px;
    }
  }

  .section-title {
    font-size: 28px;
    margin-bottom: 40px;
  }

  .company-intro {
    padding: 80px 0 60px;

    .intro-content {
      flex-direction: column;

      .intro-text {
        padding-right: 0;
        margin-bottom: 30px;

        h2 {
          font-size: 28px;
          text-align: center;
        }

        .intro-description {
          padding: 25px;
        }
      }

      .intro-image {
        width: 100%;
        height: 300px;
      }
    }
  }

  .certificates {
    padding: 80px 0 60px;

    .cert-grid {
      grid-template-columns: 1fr;
      gap: 30px;
      margin-top: 40px;
    }
  }

  .vision-mission {
    padding: 80px 0 60px;

    .vision-mission-content {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    .vision,
    .mission {
      padding: 40px 30px;

      h2 {
        font-size: 24px;

        .section-icon {
          font-size: 28px;
        }
      }

      p {
        font-size: 16px;
      }
    }
  }

  .contact-us {
    padding: 80px 0 60px;

    .contact-info {
      grid-template-columns: 1fr;
      gap: 30px;

      .contact-item {
        padding: 30px 25px;

        .contact-icon {
          width: 60px;
          height: 60px;
          font-size: 24px;
          margin-right: 20px;
        }

        div {
          h3 {
            font-size: 18px;
          }

          p {
            font-size: 15px;
          }
        }
      }
    }
  }
}
</style>
