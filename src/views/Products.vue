<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import { Icon } from "@iconify/vue";
// 引入Swiper相关组件和样式
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay, Pagination, Navigation, EffectFade } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import "swiper/css/effect-fade";

defineOptions({
  name: "Products",
});

// 创建Swiper实例引用
const swiperInstance = ref(null);

// 当前幻灯片索引（从0开始）
const activeIndex = ref(0);

// 设置当前活动幻灯片
const onSlideChange = (swiper) => {
  // 更新当前索引
  activeIndex.value = swiper.realIndex;
};

// 幻灯片切换后
const onSlideChangeTransitionEnd = () => {
  // 不再需要动画
};

// 获取Swiper实例
const onSwiper = (swiper) => {
  swiperInstance.value = swiper;
};

// 导航到特定幻灯片
const goToSlide = (index) => {
  if (swiperInstance.value) {
    swiperInstance.value.slideTo(index);
  }
};

// 页面加载动画
const isLoaded = ref(false);
const visibleItems = ref({});

onMounted(() => {
  // 设置页面已加载
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);

  // 初始化可见项
  setTimeout(() => {
    visibleItems.value = { title: true };

    // 逐步显示产品描述
    setTimeout(() => {
      visibleItems.value = { ...visibleItems.value, description: true };

      // 逐步显示产品亮点
      setTimeout(() => {
        visibleItems.value = { ...visibleItems.value, highlights: true };

        // 逐步显示核心功能
        setTimeout(() => {
          visibleItems.value = { ...visibleItems.value, features: true };
        }, 200);
      }, 200);
    }, 200);
  }, 500);
});

// 清理工作
onBeforeUnmount(() => {
  if (swiperInstance.value) {
    swiperInstance.value.destroy();
  }
});
</script>

<template>
  <div class="products" :class="{ 'is-loaded': isLoaded }">
    <!-- 产品展示 - 使用Swiper轮播图 -->
    <section class="product-carousel">
      <div class="container">
        <div class="carousel-container">
          <div class="water-effect"></div>
          <swiper
            :modules="[Autoplay, Pagination, Navigation, EffectFade]"
            :slides-per-view="1"
            :space-between="0"
            :loop="true"
            :effect="'fade'"
            :fade-effect="{ crossFade: true }"
            @swiper="onSwiper"
            @slide-change="onSlideChange"
            @transition-end="onSlideChangeTransitionEnd"
            :autoplay="{
              delay: 5000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }"
            :speed="800"
            class="product-swiper"
          >
            <!-- 防汛"四预" -->
            <swiper-slide>
              <div class="product-item">
                <div class="product-content">
                  <h2 :class="{ visible: visibleItems.title }">防汛"四预"</h2>
                  <p
                    class="product-description"
                    :class="{ visible: visibleItems.description }"
                  >
                    防汛"四预"系统是一款智能化防汛决策应用产品，通过实时监测水情，提前预测洪水风险（预报），第一时间向相关部门报送出警报（预警），同时根据洪水可能造成的各种影响（预演），启动生效量化应对方案（预案），把传统被动处置转变为主动防控，让防汛工作更科学。更安全，通过智能预报、精准预警、数字预演、智能预案的全流程闭环管理，大幅提升防汛决策效率和处置能力，同时结合AI大模型打造防汛"智能助手"，进一步统筹火灾等防控体系，帮助减少洪灾损失，提升应急响应速度，为各级政府防汛部门提供科学指导及决策支撑。
                  </p>
                  <div
                    class="product-item-list"
                    :class="{ visible: visibleItems.highlights }"
                  >
                    <h3>产品亮点</h3>
                    <ul>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:chart-bubble" class="item-icon" />
                          <span>多维数据融合可视化</span>
                        </div>
                        <div class="item-desc">
                          实现防汛工程体系、雨水情监测预报体系和防汛工作体系等各类数据的融合，在一张图上形象直观地展现水利工具、城乡分级相关基础设施的分布情况，用于用户查看疏散区域洪涝灾害风险名单
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:alarm-light" class="item-icon" />
                          <span>数字孪生预警精准化</span>
                        </div>
                        <div class="item-desc">
                          内置洪水预报模型、工程调度模型、洪水淹没分析模型等，支撑流域洪涝风险科学预测与防治，根据实测降雨和预报数据自动生成预警信息，精准追溯相关责任人，实时洪涝灾害风险网格化管理
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:robot" class="item-icon" />
                          <span>AI智能助手一键问答</span>
                        </div>
                        <div class="item-desc">
                          基于防汛问题知识和预案库，开发防汛预案智能助手，能够协助用户检索和查询数据，生成数据统计报表，辅助地图浏览和可视化分析，简化软件使用流程，提升功能易用性
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:cogs" class="item-icon" />
                          <span>业务适配扩展灵活化</span>
                        </div>
                        <div class="item-desc">
                          采用微服务框架开发，适配国产操作系统和数据库，充分考虑与相关业务系统共享需求，使于数据对接和集成，支持根据个性化业务场景进行二次开发和定制，支持移动端应用
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div
                    class="product-item-list"
                    :class="{ visible: visibleItems.features }"
                  >
                    <h3>核心功能</h3>
                    <ul>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:chart-areaspline" class="item-icon" />
                          <span>智能预报</span>
                        </div>
                        <div class="item-desc">
                          整合气象预报、精细降雨预报、河湖洪水预报和水库来水预报信息，构建多层级洪涝灾害风险预判网
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:bell-alert" class="item-icon" />
                          <span>动态预警</span>
                        </div>
                        <div class="item-desc">
                          建立直达一线的预警发布系统，适配气象和水文预警，自动跟踪防汛相关责任人及预警反馈动态
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon
                            icon="mdi:chart-timeline-variant"
                            class="item-icon"
                          />
                          <span>数字预演</span>
                        </div>
                        <div class="item-desc">
                          模拟实况、历史、设计等多种洪涝景下的淹没方案，以定性定量量化方式预估灾害的损失和影响
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:file-document" class="item-icon" />
                          <span>预案管理</span>
                        </div>
                        <div class="item-desc">
                          生成洪涝灾害防御应急处置预案，包含工程调度方案、群众避险转移方案、物资调配方案等，跟踪预案执行情况并汇总分析
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:handshake" class="item-icon" />
                          <span>协同指挥</span>
                        </div>
                        <div class="item-desc">
                          多部门联动调度（水利、应急、交通），支持移动端实时灾情上报与任务跟踪
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </swiper-slide>

            <!-- 水资源调配 -->
            <swiper-slide>
              <div class="product-item">
                <div class="product-content">
                  <h2 :class="{ visible: visibleItems.title }">水资源调配</h2>
                  <p
                    class="product-description"
                    :class="{ visible: visibleItems.description }"
                  >
                    水资源调配应用产品，为聚焦实施国家节水行动、高效水资源调度的迫切需求，以全面提升水资源管理、保障和服务能力为总体目标，国际现代水监管服务、水资源综合调度、节水管理等方面建设业务化应用，通过集成物联网、大数据、人工智能等前沿技术，实现水资源及节水监测服务的规范管治，强化水资源监控预警能力和取用水过程动态管控能力，切实提升水资源的精细化管理与智慧化服务水平，科学指导推进节水行动，支撑高严格水资源管理制度长效实施，实现水资源保障治理体系和治理能力的现代化。
                  </p>
                  <div
                    class="product-item-list"
                    :class="{ visible: visibleItems.highlights }"
                  >
                    <h3>产品亮点</h3>
                    <ul>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:database-sync" class="item-icon" />
                          <span>数据全链条互联</span>
                        </div>
                        <div class="item-desc">
                          通过水源地监管、取水许可监管、取用水监管、水量互认等专业业务数据接入，实现从水源地到水龙头的全程监控，在一张图中展示时空分布动态，协助用户分析水资源手拉手视化趋势，合理安排取用水和调度水计划
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:chart-box" class="item-icon" />
                          <span>调配方案配置优化</span>
                        </div>
                        <div class="item-desc">
                          结合需水预测模型和可供水量评估结果进行供需平衡分析，利用水资源优化配置模型，考虑居民生活、建筑业及服务业、工业、农业和生态等不同用途的供水保证率，逐步优化水资源调配模型参数及方案结果
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:robot" class="item-icon" />
                          <span>AI智能助手一键查</span>
                        </div>
                        <div class="item-desc">
                          基于水资源知识库，开发水资源AI智能助手，能够协助用户筛选和查询数据，生成数据统计报表，辅助地图浏览及可视化分析，实现水资源资料一键查询，统计数据时间问答，提升用户使用效率
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:cogs" class="item-icon" />
                          <span>业务扩展灵活化</span>
                        </div>
                        <div class="item-desc">
                          采用微服务框架开发，适配国产操作系统和数据库，充分考虑与相关业务系统共享需求，便于数据对接和集成，支持根据个性化业务场景进行二次开发和定制，支持移动端应用
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div
                    class="product-item-list"
                    :class="{ visible: visibleItems.features }"
                  >
                    <h3>核心功能</h3>
                    <ul>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:view-dashboard" class="item-icon" />
                          <span>水资源驾驶舱</span>
                        </div>
                        <div class="item-desc">
                          水资源驾驶舱包括水资源综合监控、取用水监管、水资源调配及水资源专题图，面向水资源业务和决策用户提供区域当前水资源业务管理开展情况及当前状态
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:briefcase" class="item-icon" />
                          <span>水资源业务管理</span>
                        </div>
                        <div class="item-desc">
                          水资源业务管理包括取水水源地、取水计划管理、水量指定及比对、章水调引管理、节约用水管理、水资源使用统计等功能，协助用户完成事务及报表等业务管理
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:information" class="item-icon" />
                          <span>水资源信息服务</span>
                        </div>
                        <div class="item-desc">
                          主要包括取用水户基础信息监管、地下水监管、生态水量监管、水源地水功能区监管、水资源证管理、纺织文件管理、以及水资源基础信息管理功能
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:share-variant" class="item-icon" />
                          <span>水资源调配支持</span>
                        </div>
                        <div class="item-desc">
                          主要包括水资源调配模型开发、水资源调配专题配置、调配方案编制、调配方案核定及调配结果评估，辅助用户科学制定水资源调配方案
                        </div>
                      </li>
                      <li>
                        <div class="item-title">
                          <Icon icon="mdi:robot" class="item-icon" />
                          <span>水资源智能助手</span>
                        </div>
                        <div class="item-desc">
                          基于人工智能模型和综合水资源知识库构建的业务智能化，能够根据管理用户需求快速检索信息并生成可视化图表，驱动地图定位分析展示，帮助用户整合业务信息并形成预案需要决策的流程，提高业务办理效率，优化服务体验
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>

          <!-- 自定义导航区域 -->
          <div class="carousel-navigation">
            <button
              class="carousel-control prev"
              @click="swiperInstance?.slidePrev()"
            >
              <Icon icon="mdi:chevron-left" class="arrow" />
            </button>

            <!-- 导航点 -->
            <div class="carousel-dots">
              <span
                v-for="index in 2"
                :key="index - 1"
                class="dot"
                :class="{ active: index - 1 === activeIndex }"
                @click="goToSlide(index - 1)"
              ></span>
            </div>

            <button
              class="carousel-control next"
              @click="swiperInstance?.slideNext()"
            >
              <Icon icon="mdi:chevron-right" class="arrow" />
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.products {
  min-height: 100vh;
  overflow-y: auto;
  position: relative;
  padding-bottom: 80px;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url("@/assets/images/banner2.png");
    background-size: cover;
    background-position: center;
    z-index: -1;
    filter: brightness(0.85) contrast(1.1) saturate(1.1);
    transition: all 1.2s ease;
    transform: scale(1.05);
  }

  &.is-loaded::before {
    transform: scale(1);
  }

  &::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.2)
    );
    z-index: -1;
  }
}

.container {
  margin: 0 auto;
  padding: 0 100px;
  max-width: 1600px;
}

.product-carousel {
  padding: 160px 0 100px;
}

.carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 10px 20px rgba(0, 0, 0, 0.2);
  padding: 40px 40px 80px 40px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.5s ease;

  // 水波效果
  .water-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to bottom,
      rgba(30, 136, 229, 0.05) 0%,
      rgba(30, 136, 229, 0) 100%
    );
    z-index: -1;
    opacity: 0.5;

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 200%;
      height: 100%;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.15' fill='%231e88e5'/%3E%3C/svg%3E");
      background-size: 100% 100%;
      animation: wave 25s linear infinite;
      z-index: -1;
      opacity: 0.4;
    }

    &::after {
      animation-direction: reverse;
      animation-duration: 20s;
      opacity: 0.2;
    }
  }

  &:hover {
    box-shadow: 0 25px 65px rgba(0, 0, 0, 0.35), 0 10px 25px rgba(0, 0, 0, 0.2);
    transform: translateY(-5px);
  }
}

// 波浪动画
@keyframes wave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

// Swiper样式覆盖
.product-swiper {
  width: 100%;

  .swiper-slide {
    backface-visibility: hidden;

    &.swiper-slide-active {
      z-index: 1;
    }
  }
}

.carousel-navigation {
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  z-index: 10;
}

.carousel-control {
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    rgba(30, 136, 229, 0.8),
    rgba(21, 101, 192, 0.8)
  );
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: all 0.6s;
    z-index: -1;
  }

  &:hover {
    background: linear-gradient(
      135deg,
      rgba(30, 136, 229, 1),
      rgba(21, 101, 192, 1)
    );
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(1);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .arrow {
    font-size: 24px;
    transition: transform 0.3s;
  }

  &.prev:hover .arrow {
    transform: translateX(-3px);
  }

  &.next:hover .arrow {
    transform: translateX(3px);
  }
}

.carousel-dots {
  display: flex;
  gap: 12px;
}

.dot {
  width: 14px;
  height: 14px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0);
    transition: all 0.3s ease;
  }

  &.active {
    background-color: #1e88e5;
    transform: scale(1.3);
    box-shadow: 0 0 15px rgba(30, 136, 229, 0.8);

    &::after {
      border-color: rgba(30, 136, 229, 0.5);
    }
  }

  &:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
  }
}

.product-item {
  padding: 0 30px;

  .product-content {
    max-width: 1400px;
    margin: 0 auto;
  }

  h2 {
    font-size: 42px;
    color: #fff;
    margin-bottom: 30px;
    text-align: center;
    font-weight: 600;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 3px;
      background: linear-gradient(to right, transparent, #1e88e5, transparent);
      transition: width 1s ease 0.3s;
    }

    &.visible {
      opacity: 1;
      transform: translateY(0);

      &::after {
        width: 150px;
      }
    }
  }

  .product-description {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    text-align: justify;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 70px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 30px;
    border-radius: 16px;
    border-left: 4px solid #1e88e5;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;

    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .product-item-list {
    // 除了最后一个，其他都有margin-bottom
    &:not(:last-child) {
      margin-bottom: 70px;
    }

    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.2s;

    &.visible {
      opacity: 1;
      transform: translateY(0);
    }

    h3 {
      font-size: 32px;
      color: #fff;
      text-align: center;
      margin-bottom: 50px;
      position: relative;
      display: inline-block;
      left: 50%;
      transform: translateX(-50%);

      &::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(
          to right,
          transparent,
          #1e88e5,
          transparent
        );
      }
    }

    ul {
      list-style: none;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 40px 100px;

      li {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 12px;
        position: relative;
        line-height: 1.6;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        padding: 25px;
        transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(5px);
        transform-origin: center;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
          border-color: rgba(30, 136, 229, 0.3);

          .item-icon {
            transform: rotate(10deg) scale(1.2);
            color: #64b5f6;
          }
        }

        .item-title {
          display: flex;
          align-items: center;
          font-weight: bold;
          margin-bottom: 16px;
          font-size: 24px;
          color: #90caf9;
          position: relative;

          .item-icon {
            margin-right: 15px;
            font-size: 30px;
            color: #1e88e5;
            transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
            filter: drop-shadow(0 0 8px rgba(30, 136, 229, 0.5));
          }

          span {
            position: relative;

            &::after {
              content: "";
              position: absolute;
              bottom: -4px;
              left: 0;
              width: 0;
              height: 2px;
              background-color: #64b5f6;
              transition: width 0.5s ease;
            }
          }
        }

        &:hover .item-title span::after {
          width: 100%;
        }

        .item-desc {
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.8;
        }
      }
    }
  }
}

// 添加媒体查询，优化响应式布局
@media (max-width: 1400px) {
  .container {
    padding: 0 50px;
  }

  .product-item .product-description {
    max-width: 90%;
  }

  .product-item .product-item-list ul {
    gap: 30px 60px;
  }
}

@media (max-width: 1200px) {
  .product-item .product-item-list ul {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .page-title h1 {
    font-size: 36px;
  }

  .product-item h2 {
    font-size: 36px;
  }

  .carousel-container {
    padding: 30px 30px 70px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .carousel-container {
    padding: 30px 15px 60px;
    border-radius: 16px;
  }

  .product-item {
    padding: 0 10px;
  }

  .product-carousel {
    padding: 120px 0 80px;
  }

  .page-title {
    padding: 60px 0 20px;

    h1 {
      font-size: 32px;
    }
  }

  .product-item h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }

  .product-item .product-description {
    max-width: 100%;
    font-size: 16px;
    padding: 20px;
    margin-bottom: 50px;
  }

  .product-item .product-item-list h3 {
    font-size: 26px;
    margin-bottom: 30px;
  }

  .product-item .product-item-list ul li {
    padding: 20px;

    .item-title {
      font-size: 20px;

      .item-icon {
        font-size: 24px;
      }
    }
  }

  .carousel-navigation {
    .carousel-control {
      width: 40px;
      height: 40px;

      .arrow {
        font-size: 20px;
      }
    }
  }

  .carousel-dots {
    gap: 10px;

    .dot {
      width: 12px;
      height: 12px;
    }
  }
}
</style>
