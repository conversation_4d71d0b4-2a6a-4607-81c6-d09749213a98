<template>
  <nav class="navbar">
    <div class="container">
      <div class="logo">
        <router-link to="/">
          <img src="@/assets/images/logo.png" alt="怀川科技" class="logo-img" />
          <span class="logo-text">怀川科技</span>
        </router-link>
      </div>
      <div class="menu">
        <ul>
          <li>
            <router-link to="/products">
              <Icon icon="mdi:package-variant" class="nav-icon" />
              产品
            </router-link>
          </li>
          <li>
            <router-link to="/solutions">
              <Icon icon="mdi:lightbulb-on" class="nav-icon" />
              解决方案
            </router-link>
          </li>
          <li>
            <router-link to="/about">
              <Icon icon="mdi:information" class="nav-icon" />
              关于我们
            </router-link>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { Icon } from '@iconify/vue';

defineOptions({
  name: "NavBar",
});
</script>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  .logo {
    a {
      display: flex;
      align-items: center;
      text-decoration: none;
    }

    .logo-img {
      height: 40px;
    }

    .logo-text {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      margin-left: 10px;
    }
  }

  .menu {
    ul {
      display: flex;
      list-style: none;

      li {
        margin-left: 30px;

        a {
          color: #fff;
          font-size: 16px;
          text-decoration: none;
          position: relative;
          display: flex;
          align-items: center;

          .nav-icon {
            margin-right: 5px;
            font-size: 20px;
          }

          &:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            background: #fff;
            left: 0;
            bottom: -5px;
            transition: width 0.3s;
          }

          &:hover:after,
          &.router-link-active:after {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>