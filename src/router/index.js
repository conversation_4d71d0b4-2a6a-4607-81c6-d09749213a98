import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
const Home = () => import('@/views/Home.vue')
const About = () => import('@/views/About.vue')
const Products = () => import('@/views/Products.vue')
const Solutions = () => import('@/views/Solutions.vue')

// 定义路由
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/products',
    name: 'Products',
    component: Products
  },
  {
    path: '/solutions',
    name: 'Solutions',
    component: Solutions
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router