// �R�q�;�ri�H
:root {
  // ;r - q��r�
  --primary-green: #2d5a27;        // �q�
  --mountain-green: #3e6b3e;       // q��
  --forest-green: #4a7c59;         // ��
  --bamboo-green: #6b9080;         // �R�
  --spring-green: #8db596;         // %�
  --jade-green: #a6c3a0;           // ���

  // 4�ri
  --deep-water: #1e3a5f;           // �4�
  --river-blue: #5ba3d4;           // _��
  --lake-blue: #a4c3d2;            // V��
  --sky-blue: #c7e2f7;             // )z�
  --mist-blue: #e8f4fd;            // �-�

  // рri
  --tech-blue: #1e88e5;            // р�
  --digital-cyan: #00bcd4;         // pWR
  --innovation-purple: #9c27b0;    // �+

  // '0r�
  --earth-brown: #6d4c41;          // '0�
  --stone-gray: #5d4037;           // �p
  --sunset-orange: #ff8f00;        // 3Y
  --golden-yellow: #ffc107;        // ��r

  // -'r
  --white: #ffffff;
  --light-gray: #f8fffe;           // ��}
  --medium-gray: #e0e6e5;          // -'p
  --dark-gray: #2c3e50;            // �p
  --black: #1a1a1a;

  // ��S
  --primary-green-10: rgba(45, 90, 39, 0.1);
  --primary-green-20: rgba(45, 90, 39, 0.2);
  --primary-green-50: rgba(45, 90, 39, 0.5);
  --primary-green-80: rgba(45, 90, 39, 0.8);

  --tech-blue-10: rgba(30, 136, 229, 0.1);
  --tech-blue-20: rgba(30, 136, 229, 0.2);
  --tech-blue-30: rgba(30, 136, 229, 0.3);
  --tech-blue-50: rgba(30, 136, 229, 0.5);

  // �r
  --gradient-mountain: linear-gradient(135deg, var(--primary-green), var(--forest-green));
  --gradient-water: linear-gradient(135deg, var(--deep-water), var(--river-blue));
  --gradient-tech: linear-gradient(135deg, var(--tech-blue), var(--digital-cyan));
  --gradient-earth: linear-gradient(135deg, var(--earth-brown), var(--stone-gray));
  --gradient-sky: linear-gradient(135deg, var(--sky-blue), var(--mist-blue));

  // 4q��
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 24px 48px rgba(0, 0, 0, 0.3);

  // yH4q
  --glow-green: 0 0 20px rgba(107, 144, 128, 0.3);
  --glow-blue: 0 0 20px rgba(30, 136, 229, 0.3);
  --glow-tech: 0 0 30px rgba(0, 188, 212, 0.4);

  // WS'
  --text-xs: 0.75rem;    // 12px
  --text-sm: 0.875rem;   // 14px
  --text-base: 1rem;     // 16px
  --text-lg: 1.125rem;   // 18px
  --text-xl: 1.25rem;    // 20px
  --text-2xl: 1.5rem;    // 24px
  --text-3xl: 1.875rem;  // 30px
  --text-4xl: 2.25rem;   // 36px
  --text-5xl: 3rem;      // 48px
  --text-6xl: 3.75rem;   // 60px

  // ����
  --space-1: 0.25rem;    // 4px
  --space-2: 0.5rem;     // 8px
  --space-3: 0.75rem;    // 12px
  --space-4: 1rem;       // 16px
  --space-5: 1.25rem;    // 20px
  --space-6: 1.5rem;     // 24px
  --space-8: 2rem;       // 32px
  --space-10: 2.5rem;    // 40px
  --space-12: 3rem;      // 48px
  --space-16: 4rem;      // 64px
  --space-20: 5rem;      // 80px
  --space-24: 6rem;      // 96px
  --space-32: 8rem;      // 128px

  // ���
  --radius-sm: 0.25rem;  // 4px
  --radius-md: 0.5rem;   // 8px
  --radius-lg: 0.75rem;  // 12px
  --radius-xl: 1rem;     // 16px
  --radius-2xl: 1.5rem;  // 24px
  --radius-full: 9999px;

  // �!�;
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  // Z-index B�
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// SCSS ��|�
$primary-green: var(--primary-green);
$mountain-green: var(--mountain-green);
$forest-green: var(--forest-green);
$bamboo-green: var(--bamboo-green);
$spring-green: var(--spring-green);
$jade-green: var(--jade-green);

$deep-water: var(--deep-water);
$river-blue: var(--river-blue);
$lake-blue: var(--lake-blue);
$sky-blue: var(--sky-blue);
$mist-blue: var(--mist-blue);

$tech-blue: var(--tech-blue);
$digital-cyan: var(--digital-cyan);
$innovation-purple: var(--innovation-purple);

$earth-brown: var(--earth-brown);
$stone-gray: var(--stone-gray);
$sunset-orange: var(--sunset-orange);
$golden-yellow: var(--golden-yellow);

// ��
@mixin glass-effect($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@mixin mountain-shadow() {
  box-shadow:
    0 4px 8px rgba(45, 90, 39, 0.1),
    0 8px 16px rgba(45, 90, 39, 0.05);
}

@mixin water-ripple() {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(91, 163, 212, 0.3),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }
}

@mixin floating-animation($duration: 3s, $distance: 20px) {
  animation: float-#{$duration}-#{$distance} $duration ease-in-out infinite;

  @keyframes float-#{$duration}-#{$distance} {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-#{$distance});
    }
  }
}