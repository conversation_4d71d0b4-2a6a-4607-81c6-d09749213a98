import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

// 护青绿山川主题动画效果
export class MountainRiverAnimations {
  constructor() {
    this.tl = gsap.timeline()
  }

  // 山川层叠进入效果
  mountainLayersEntry(selector, options = {}) {
    const defaults = {
      duration: 1.2,
      stagger: 0.2,
      ease: "power3.out"
    }
    const config = { ...defaults, ...options }

    return gsap.fromTo(selector, 
      {
        y: 100,
        opacity: 0,
        scale: 0.95,
        rotationX: 15
      },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        rotationX: 0,
        duration: config.duration,
        stagger: config.stagger,
        ease: config.ease
      }
    )
  }

  // 水波纹扩散效果
  waterRippleEffect(selector, options = {}) {
    const defaults = {
      duration: 2,
      repeat: -1,
      ease: "power2.inOut"
    }
    const config = { ...defaults, ...options }

    return gsap.fromTo(selector,
      {
        scale: 0.8,
        opacity: 0.8
      },
      {
        scale: 1.2,
        opacity: 0,
        duration: config.duration,
        repeat: config.repeat,
        ease: config.ease
      }
    )
  }

  // 浮动元素动画
  floatingElements(selector, options = {}) {
    const defaults = {
      duration: 3,
      yoyo: true,
      repeat: -1,
      ease: "power2.inOut"
    }
    const config = { ...defaults, ...options }

    return gsap.to(selector, {
      y: -20,
      rotation: 5,
      duration: config.duration,
      yoyo: config.yoyo,
      repeat: config.repeat,
      ease: config.ease,
      stagger: {
        each: 0.5,
        from: "random"
      }
    })
  }

  // 文字逐字显现效果
  textReveal(selector, options = {}) {
    const defaults = {
      duration: 0.05,
      stagger: 0.05,
      ease: "power2.out"
    }
    const config = { ...defaults, ...options }

    // 将文字分割成单个字符
    const element = document.querySelector(selector)
    if (!element) return

    const text = element.textContent
    element.innerHTML = text.split('').map(char => 
      `<span style="display: inline-block; opacity: 0; transform: translateY(50px);">${char === ' ' ? '&nbsp;' : char}</span>`
    ).join('')

    return gsap.to(`${selector} span`, {
      opacity: 1,
      y: 0,
      duration: config.duration,
      stagger: config.stagger,
      ease: config.ease
    })
  }

  // 粒子效果背景
  particleBackground(container, options = {}) {
    const defaults = {
      particleCount: 50,
      colors: ['#2d5a27', '#4a7c59', '#6b9080', '#a4c3d2', '#1e88e5'],
      size: { min: 2, max: 6 },
      speed: { min: 0.5, max: 2 }
    }
    const config = { ...defaults, ...options }

    const particles = []
    const containerEl = document.querySelector(container)
    
    if (!containerEl) return

    // 创建粒子
    for (let i = 0; i < config.particleCount; i++) {
      const particle = document.createElement('div')
      particle.className = 'particle'
      particle.style.cssText = `
        position: absolute;
        width: ${gsap.utils.random(config.size.min, config.size.max)}px;
        height: ${gsap.utils.random(config.size.min, config.size.max)}px;
        background: ${gsap.utils.random(config.colors)};
        border-radius: 50%;
        opacity: ${gsap.utils.random(0.1, 0.8)};
        pointer-events: none;
        z-index: 1;
      `
      
      containerEl.appendChild(particle)
      particles.push(particle)

      // 随机位置
      gsap.set(particle, {
        x: gsap.utils.random(0, window.innerWidth),
        y: gsap.utils.random(0, window.innerHeight)
      })

      // 浮动动画
      gsap.to(particle, {
        x: `+=${gsap.utils.random(-100, 100)}`,
        y: `+=${gsap.utils.random(-100, 100)}`,
        duration: gsap.utils.random(config.speed.min, config.speed.max) * 10,
        repeat: -1,
        yoyo: true,
        ease: "none"
      })
    }

    return particles
  }

  // 视差滚动效果
  parallaxScroll(selector, options = {}) {
    const defaults = {
      speed: 0.5,
      ease: "none"
    }
    const config = { ...defaults, ...options }

    return gsap.to(selector, {
      yPercent: -50 * config.speed,
      ease: config.ease,
      scrollTrigger: {
        trigger: selector,
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    })
  }

  // 卡片翻转进入效果
  cardFlipEntry(selector, options = {}) {
    const defaults = {
      duration: 0.8,
      stagger: 0.15,
      ease: "back.out(1.7)"
    }
    const config = { ...defaults, ...options }

    return gsap.fromTo(selector,
      {
        rotationY: -90,
        opacity: 0,
        transformOrigin: "left center"
      },
      {
        rotationY: 0,
        opacity: 1,
        duration: config.duration,
        stagger: config.stagger,
        ease: config.ease,
        scrollTrigger: {
          trigger: selector,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      }
    )
  }

  // 数字滚动效果
  countUpAnimation(selector, finalNumber, options = {}) {
    const defaults = {
      duration: 2,
      ease: "power2.out"
    }
    const config = { ...defaults, ...options }

    const obj = { value: 0 }
    const element = document.querySelector(selector)
    
    if (!element) return

    return gsap.to(obj, {
      value: finalNumber,
      duration: config.duration,
      ease: config.ease,
      onUpdate: function() {
        element.textContent = Math.floor(obj.value)
      },
      scrollTrigger: {
        trigger: selector,
        start: "top 80%",
        toggleActions: "play none none reverse"
      }
    })
  }

  // 磁性按钮效果
  magneticButton(selector, options = {}) {
    const defaults = {
      strength: 0.3,
      ease: "power2.out"
    }
    const config = { ...defaults, ...options }

    const buttons = document.querySelectorAll(selector)
    
    buttons.forEach(button => {
      button.addEventListener('mousemove', (e) => {
        const rect = button.getBoundingClientRect()
        const x = e.clientX - rect.left - rect.width / 2
        const y = e.clientY - rect.top - rect.height / 2
        
        gsap.to(button, {
          x: x * config.strength,
          y: y * config.strength,
          duration: 0.3,
          ease: config.ease
        })
      })

      button.addEventListener('mouseleave', () => {
        gsap.to(button, {
          x: 0,
          y: 0,
          duration: 0.5,
          ease: "elastic.out(1, 0.3)"
        })
      })
    })
  }

  // 页面切换过渡
  pageTransition(options = {}) {
    const defaults = {
      duration: 0.8,
      ease: "power2.inOut"
    }
    const config = { ...defaults, ...options }

    const tl = gsap.timeline()
    
    // 创建遮罩层
    const overlay = document.createElement('div')
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2d5a27, #4a7c59);
      z-index: 9999;
      transform: translateY(-100%);
    `
    document.body.appendChild(overlay)

    tl.to(overlay, {
      y: 0,
      duration: config.duration / 2,
      ease: config.ease
    })
    .to(overlay, {
      y: '100%',
      duration: config.duration / 2,
      ease: config.ease,
      onComplete: () => {
        document.body.removeChild(overlay)
      }
    })

    return tl
  }

  // 滚动触发的时间线
  createScrollTimeline(triggers) {
    triggers.forEach(trigger => {
      ScrollTrigger.create({
        trigger: trigger.element,
        start: trigger.start || "top 80%",
        end: trigger.end || "bottom 20%",
        onEnter: trigger.onEnter,
        onLeave: trigger.onLeave,
        onEnterBack: trigger.onEnterBack,
        onLeaveBack: trigger.onLeaveBack,
        ...trigger.options
      })
    })
  }

  // 清理所有动画
  cleanup() {
    ScrollTrigger.getAll().forEach(st => st.kill())
    gsap.killTweensOf("*")
  }
}

// 预设动画组合
export const presetAnimations = {
  // 首页英雄区域
  heroSection: (selector) => {
    const animations = new MountainRiverAnimations()
    const tl = gsap.timeline()
    
    tl.add(animations.mountainLayersEntry(`${selector} .hero-title`))
      .add(animations.textReveal(`${selector} .hero-subtitle`), "-=0.5")
      .add(animations.floatingElements(`${selector} .floating-element`), "-=1")
    
    return tl
  },

  // 关于页面介绍区域
  aboutSection: (selector) => {
    const animations = new MountainRiverAnimations()
    return animations.cardFlipEntry(`${selector} .intro-card`)
  },

  // 产品展示区域
  productSection: (selector) => {
    const animations = new MountainRiverAnimations()
    const tl = gsap.timeline()
    
    tl.add(animations.mountainLayersEntry(`${selector} .product-item`))
      .add(animations.waterRippleEffect(`${selector} .ripple-effect`), "-=0.8")
    
    return tl
  }
}

// 全局初始化函数
export function initMountainRiverAnimations() {
  // 设置默认值
  gsap.defaults({
    ease: "power2.out",
    duration: 0.8
  })

  // 预加载关键动画
  gsap.set(['.mountain-layer', '.water-element', '.floating-icon'], {
    opacity: 0,
    y: 50
  })

  // 页面加载完成后的初始动画
  window.addEventListener('load', () => {
    const animations = new MountainRiverAnimations()
    animations.mountainLayersEntry('.mountain-layer')
    animations.floatingElements('.floating-icon')
  })
}